using Godot;
using System;

/// <summary>
/// GreenBush2 object that can be destroyed by pickaxe
/// Handles hit animations, health management, and resource dropping
/// Drops 2 Leaves and 1 Branch when destroyed
/// </summary>
public partial class GreenBush2 : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 4;

	// Event for when green bush is destroyed
	[Signal] public delegate void GreenBush2DestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;

	// Hit animation properties
	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f); // White tint
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f; // 50% white tint

	public override void _Ready()
	{
		_currentHealth = MaxHealth;
		_sprite = GetNode<Sprite2D>("GreenBushSprite");
		_hpBar = GetNode<ProgressBar>("ProgressBar");

		// Initially hide the HP bar
		if (_hpBar != null)
		{
			_hpBar.Visible = false;
		}

		// Connect to pickaxe usage signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	public override void _ExitTree()
	{
		// Disconnect from signals when the node is removed
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, Vector2 playerPosition, int damage)
	{
		// Check if this green bush is at the target tile position
		if (tilePosition == _tilePosition)
		{
			TakeDamage(damage);
		}
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		ShowHPBar();
		PlayHitAnimation();

		GD.Print($"GreenBush2 took {damage} damage! Health: {_currentHealth}/{MaxHealth}");

		if (_currentHealth <= 0)
		{
			DestroyGreenBush();
		}
	}

	private void UpdateHPBar()
	{
		if (_hpBar != null)
		{
			_hpBar.Value = (float)_currentHealth / MaxHealth * 100;
		}
	}

	private void ShowHPBar()
	{
		if (_hpBar != null)
		{
			_hpBar.Visible = true;

			// Only hide HP bar after 3 seconds if health is at maximum
			if (_currentHealth >= MaxHealth)
			{
				GetTree().CreateTimer(3.0f).Timeout += () => {
					if (_hpBar != null && IsInstanceValid(this) && _currentHealth >= MaxHealth)
					{
						_hpBar.Visible = false;
					}
				};
			}
			// If health is below max, keep it visible permanently
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		// Stop any existing tween
		_hitTween?.Kill();

		// Create new tween for hit animation
		_hitTween = CreateTween();
		_hitTween.SetEase(Tween.EaseType.Out);
		_hitTween.SetTrans(Tween.TransitionType.Quart);

		// Animate to hit color, then back to normal
		var hitColorWithTint = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColorWithTint, _hitAnimationDuration / 2);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration / 2);
	}

	private void DestroyGreenBush()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		GD.Print($"GreenBush2 destroyed at {_tilePosition}!");

		// Drop resources
		DropResources();

		// Clear the tile data
		if (_customDataManager != null)
		{
			_customDataManager.ClearObjectPlaced(_tilePosition);
		}

		// Emit signal that green bush was destroyed
		EmitSignal(SignalName.GreenBush2Destroyed, _tilePosition);

		// Remove the green bush from the scene
		QueueFree();
	}

	private void DropResources()
	{
		// Drop 2 Leaves
		for (int i = 0; i < 2; i++)
		{
			CreateDroppedResource(ResourceType.Leaf, 1);
		}

		// Drop 1 Branch
		CreateDroppedResource(ResourceType.Branch, 1);
	}

	private void CreateDroppedResource(ResourceType resourceType, int amount)
	{
		var droppedResourceScene = GD.Load<PackedScene>("res://scenes/mapObjects/DroppedResource.tscn");
		if (droppedResourceScene != null)
		{
			var droppedResource = droppedResourceScene.Instantiate<DroppedResource>();
			if (droppedResource != null)
			{
				// Add some random offset to prevent resources from stacking exactly
				Vector2 randomOffset = new Vector2(
					(float)(GD.Randf() - 0.5f) * 8, // Random offset between -4 and 4 pixels
					(float)(GD.Randf() - 0.5f) * 8
				);
				droppedResource.GlobalPosition = GlobalPosition + randomOffset;
				droppedResource.SetResource(resourceType, amount);

				// Add to the world
				GetParent().AddChild(droppedResource);
			}
		}
	}

	public void SetTilePosition(Vector2I tilePosition)
	{
		_tilePosition = tilePosition;
	}

	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public void SetCustomDataManager(CustomDataLayerManager customDataManager)
	{
		_customDataManager = customDataManager;
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		// Green bushes can be hit from adjacent tiles (including diagonally)
		float distance = playerTilePosition.DistanceTo(_tilePosition);
		return distance <= 1.5f; // Allow diagonal hits
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = health;
		UpdateHPBar();
	}
}
